"use client";

import { useState } from 'react';
import { FiSave, FiEdit3, FiX, <PERSON>Eye, FiFileText } from 'react-icons/fi';

interface LatestArticlesContent {
  badge: {
    en: string;
    ar: string;
  };
  title: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
}

export default function HomePageLatestArticlesManagement() {
  const [latestArticlesContent, setLatestArticlesContent] = useState<LatestArticlesContent>({
    badge: {
      en: "Latest Insights",
      ar: "أحدث الرؤى"
    },
    title: {
      en: "Latest Articles",
      ar: "أحدث المقالات"
    },
    description: {
      en: "Stay updated with our latest news and insights in real estate development",
      ar: "ابق على اطلاع بأحدث أخبارنا ورؤانا في التطوير العقاري"
    }
  });

  const [editingSection, setEditingSection] = useState<string | null>(null);

  const handleSaveAll = () => {
    console.log('Saving latest articles content:', latestArticlesContent);
    alert('Latest Articles section content saved successfully!');
  };

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">Home Page - Latest Articles Section</h1>
          <p className="text-gray-400 mt-1">Manage the latest articles section header content</p>
        </div>
        <button
          onClick={handleSaveAll}
          className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          <FiSave className="mr-2 h-4 w-4" />
          Save All Changes
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Section Header Content */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiFileText className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Section Header Content
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'header' ? null : 'header')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'header' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'header' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'header' ? (
            <div className="space-y-6">
              {/* Badge */}
              <div>
                <h3 className="text-lg font-medium text-white mb-3">Section Badge</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Badge (English)</label>
                    <input
                      type="text"
                      value={latestArticlesContent.badge.en}
                      onChange={(e) => setLatestArticlesContent(prev => ({
                        ...prev,
                        badge: { ...prev.badge, en: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Badge (Arabic)</label>
                    <input
                      type="text"
                      value={latestArticlesContent.badge.ar}
                      onChange={(e) => setLatestArticlesContent(prev => ({
                        ...prev,
                        badge: { ...prev.badge, ar: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                </div>
              </div>

              {/* Title */}
              <div>
                <h3 className="text-lg font-medium text-white mb-3">Section Title</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
                    <input
                      type="text"
                      value={latestArticlesContent.title.en}
                      onChange={(e) => setLatestArticlesContent(prev => ({
                        ...prev,
                        title: { ...prev.title, en: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
                    <input
                      type="text"
                      value={latestArticlesContent.title.ar}
                      onChange={(e) => setLatestArticlesContent(prev => ({
                        ...prev,
                        title: { ...prev.title, ar: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                </div>
              </div>

              {/* Description */}
              <div>
                <h3 className="text-lg font-medium text-white mb-3">Section Description</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
                    <textarea
                      value={latestArticlesContent.description.en}
                      onChange={(e) => setLatestArticlesContent(prev => ({
                        ...prev,
                        description: { ...prev.description, en: e.target.value }
                      }))}
                      rows={4}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
                    <textarea
                      value={latestArticlesContent.description.ar}
                      onChange={(e) => setLatestArticlesContent(prev => ({
                        ...prev,
                        description: { ...prev.description, ar: e.target.value }
                      }))}
                      rows={4}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Badge Display */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-3">Current Badge</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <div className="inline-block px-4 py-1 rounded-full bg-[#00C2FF]/20 backdrop-blur-sm text-[#00C2FF] text-sm font-medium">
                      {latestArticlesContent.badge.en}
                    </div>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <div className="inline-block px-4 py-1 rounded-full bg-[#00C2FF]/20 backdrop-blur-sm text-[#00C2FF] text-sm font-medium text-right" dir="rtl">
                      {latestArticlesContent.badge.ar}
                    </div>
                  </div>
                </div>
              </div>

              {/* Title Display */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-3">Current Title</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <div className="bg-gray-700 p-3 rounded">
                      <h2 className="text-3xl md:text-4xl font-bold text-white relative">
                        {latestArticlesContent.title.en}
                        <span className="absolute bottom-1 start-0 w-full h-[2px] bg-gradient-to-r from-[#00C2FF] to-[#8B5CF6]"></span>
                      </h2>
                    </div>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <div className="bg-gray-700 p-3 rounded text-right" dir="rtl">
                      <h2 className="text-3xl md:text-4xl font-bold text-white relative">
                        {latestArticlesContent.title.ar}
                        <span className="absolute bottom-1 start-0 w-full h-[2px] bg-gradient-to-r from-[#00C2FF] to-[#8B5CF6]"></span>
                      </h2>
                    </div>
                  </div>
                </div>
              </div>

              {/* Description Display */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-3">Current Description</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <p className="text-gray-300 bg-gray-700 p-3 rounded text-lg">{latestArticlesContent.description.en}</p>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <p className="text-gray-300 bg-gray-700 p-3 rounded text-lg text-right" dir="rtl">{latestArticlesContent.description.ar}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Live Preview Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-white flex items-center mb-4">
            <FiEye className="mr-2 h-5 w-5 text-[#00C2FF]" />
            Live Preview
          </h2>
          <div className="bg-gray-900 rounded-lg p-8 border border-gray-600">
            <div className="text-center mb-12">
              <div className="inline-block px-4 py-1 rounded-full bg-[#00C2FF]/20 backdrop-blur-sm text-[#00C2FF] text-sm font-medium mb-4">
                {latestArticlesContent.badge.en}
              </div>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                <span className="relative text-white">
                  {latestArticlesContent.title.en}
                  <span className="absolute bottom-1 start-0 w-full h-[2px] bg-gradient-to-r from-[#00C2FF] to-[#8B5CF6]"></span>
                </span>
              </h2>
              <p className="max-w-3xl mx-auto text-gray-300 text-lg">
                {latestArticlesContent.description.en}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 