"use client";

import { useState, useEffect } from 'react';
import { FiSave, FiEdit3, FiX, FiEye, FiFileText, FiLoader } from 'react-icons/fi';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/contexts/ToastContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { createToast } from '@/utils/toast';
import { authenticatedApiCall } from '@/utils/api';

interface LatestArticlesContent {
  id?: number;
  badge: {
    en: string;
    ar: string;
  };
  title: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  created_at?: string;
  updated_at?: string;
  updated_by?: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
  };
}

export default function HomePageLatestArticlesManagement() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const { showToast } = useToast();
  const { locale } = useLanguage();

  const [latestArticlesContent, setLatestArticlesContent] = useState<LatestArticlesContent>({
    badge: {
      en: "Latest Insights",
      ar: "أحدث الرؤى"
    },
    title: {
      en: "Latest Articles",
      ar: "أحدث المقالات"
    },
    description: {
      en: "Stay updated with our latest news and insights in real estate development",
      ar: "ابق على اطلاع بأحدث أخبارنا ورؤانا في التطوير العقاري"
    }
  });

  const [originalContent, setOriginalContent] = useState<LatestArticlesContent>({
    badge: { en: "", ar: "" },
    title: { en: "", ar: "" },
    description: { en: "", ar: "" }
  });

  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Track changes
  useEffect(() => {
    const contentChanged = JSON.stringify(latestArticlesContent) !== JSON.stringify(originalContent);
    setHasChanges(contentChanged);
  }, [latestArticlesContent, originalContent]);

  // Fetch latest articles content when authentication is ready
  useEffect(() => {
    // Only fetch when auth is not loading and user is authenticated
    if (!authLoading && isAuthenticated) {
      fetchLatestArticlesContent();
    } else if (!authLoading && !isAuthenticated) {
      // Auth is ready but user is not authenticated
      setIsLoading(false);
      showToast(createToast.error(
        'Authentication required',
        'Please log in to access this page'
      ));
    }
  }, [authLoading, isAuthenticated]);

  const fetchLatestArticlesContent = async () => {
    setIsLoading(true);
    try {
      console.log('🔄 Making authenticated API call to fetch latest articles content...');
      const response = await authenticatedApiCall<LatestArticlesContent>('/api/admin/home-page/latest-articles/', {
        method: 'GET'
      });

      console.log('📥 Latest articles content response:', response);

      if (response.success && response.data) {
        setLatestArticlesContent(response.data);
        setOriginalContent(response.data);
        setHasChanges(false);
      } else {
        // Check if it's an authentication error
        if (response.message?.includes('Authentication') || response.message?.includes('Unauthorized')) {
          showToast(createToast.error(
            'Authentication expired',
            'Please log in again to continue'
          ));
        } else {
          showToast(createToast.error(
            response.message || 'Failed to load latest articles content',
            'Please try refreshing the page'
          ));
        }
      }
    } catch (error) {
      console.error('Error fetching latest articles content:', error);
      showToast(createToast.error(
        'Failed to load latest articles content',
        'Please check your connection and try again'
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveAll = async () => {
    console.log('🚀 Save button clicked');
    console.log('📊 Current state:', {
      hasChanges,
      isAuthenticated,
      isSaving
    });

    if (!hasChanges) {
      console.log('❌ No changes to save');
      showToast(createToast.info('No changes to save'));
      return;
    }

    // Check authentication before saving
    if (!isAuthenticated) {
      console.log('❌ Not authenticated');
      showToast(createToast.error(
        'Authentication required',
        'Please log in to save changes'
      ));
      return;
    }

    console.log('✅ Starting save process...');
    setIsSaving(true);
    try {
      const response = await authenticatedApiCall<LatestArticlesContent>('/api/admin/home-page/latest-articles/', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          badge: latestArticlesContent.badge,
          title: latestArticlesContent.title,
          description: latestArticlesContent.description
        })
      });

      console.log('📥 Save response:', response);

      if (response.success) {
        showToast(createToast.success(
          response.message || 'Latest articles section updated successfully',
          'All changes have been saved'
        ));

        // Update the content with the response data
        if (response.data) {
          setLatestArticlesContent(response.data);
          setOriginalContent(response.data);
        }

        setHasChanges(false);
        setEditingSection(null);
      } else {
        // Check if it's an authentication error
        if (response.message?.includes('Authentication') || response.message?.includes('Unauthorized')) {
          showToast(createToast.error(
            'Authentication expired',
            'Please log in again to continue'
          ));
        } else {
          showToast(createToast.error(
            response.message || 'Failed to save changes',
            response.errors ? Object.values(response.errors).flat().join(', ') : 'Please try again'
          ));
        }
      }
    } catch (error) {
      console.error('Error saving latest articles content:', error);
      showToast(createToast.error(
        'Failed to save changes',
        'Please check your connection and try again'
      ));
    } finally {
      setIsSaving(false);
    }
  };

  const handleSectionSave = async (section: string, data: any) => {
    // Check authentication before saving
    if (!isAuthenticated) {
      showToast(createToast.error(
        'Authentication required',
        'Please log in to save changes'
      ));
      return;
    }

    try {
      const response = await authenticatedApiCall('/api/admin/home-page/latest-articles/section/', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section,
          data
        })
      });

      if (response.success) {
        showToast(createToast.success(
          `${section.charAt(0).toUpperCase() + section.slice(1)} updated successfully`
        ));

        // Update the content with the response data
        if (response.data) {
          setLatestArticlesContent(response.data);
          setOriginalContent(response.data);
        }

        setEditingSection(null);
        setHasChanges(false);
      } else {
        showToast(createToast.error(
          response.message || `Failed to update ${section}`,
          response.errors ? Object.values(response.errors).flat().join(', ') : 'Please try again'
        ));
      }
    } catch (error) {
      console.error(`Error updating ${section}:`, error);
      showToast(createToast.error(
        `Failed to update ${section}`,
        'Please check your connection and try again'
      ));
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <FiLoader className="animate-spin h-6 w-6 text-[#00C2FF]" />
          <span className="text-gray-400">Loading latest articles content...</span>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">Home Page - Latest Articles Section</h1>
          <p className="text-gray-400 mt-1">Manage the latest articles section header content</p>
        </div>
        <button
          onClick={handleSaveAll}
          disabled={!hasChanges || isSaving || !isAuthenticated}
          className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors ${
            hasChanges && !isSaving && isAuthenticated
              ? 'bg-[#00C2FF] text-white hover:bg-[#00C2FF]/90'
              : 'bg-gray-600 text-gray-400 cursor-not-allowed'
          }`}
        >
          {isSaving ? (
            <FiLoader className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <FiSave className="mr-2 h-4 w-4" />
          )}
          {isSaving ? 'Saving...' : 'Save All Changes'}
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Section Header Content */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiFileText className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Section Header Content
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'header' ? null : 'header')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'header' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'header' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'header' ? (
            <div className="space-y-6">
              {/* Badge */}
              <div>
                <h3 className="text-lg font-medium text-white mb-3">Section Badge</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Badge (English)</label>
                    <input
                      type="text"
                      value={latestArticlesContent.badge.en}
                      onChange={(e) => setLatestArticlesContent(prev => ({
                        ...prev,
                        badge: { ...prev.badge, en: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Badge (Arabic)</label>
                    <input
                      type="text"
                      value={latestArticlesContent.badge.ar}
                      onChange={(e) => setLatestArticlesContent(prev => ({
                        ...prev,
                        badge: { ...prev.badge, ar: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                </div>
              </div>

              {/* Title */}
              <div>
                <h3 className="text-lg font-medium text-white mb-3">Section Title</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
                    <input
                      type="text"
                      value={latestArticlesContent.title.en}
                      onChange={(e) => setLatestArticlesContent(prev => ({
                        ...prev,
                        title: { ...prev.title, en: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
                    <input
                      type="text"
                      value={latestArticlesContent.title.ar}
                      onChange={(e) => setLatestArticlesContent(prev => ({
                        ...prev,
                        title: { ...prev.title, ar: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                </div>
              </div>

              {/* Description */}
              <div>
                <h3 className="text-lg font-medium text-white mb-3">Section Description</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
                    <textarea
                      value={latestArticlesContent.description.en}
                      onChange={(e) => setLatestArticlesContent(prev => ({
                        ...prev,
                        description: { ...prev.description, en: e.target.value }
                      }))}
                      rows={4}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
                    <textarea
                      value={latestArticlesContent.description.ar}
                      onChange={(e) => setLatestArticlesContent(prev => ({
                        ...prev,
                        description: { ...prev.description, ar: e.target.value }
                      }))}
                      rows={4}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleSectionSave('badge', latestArticlesContent.badge)}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                >
                  Save Badge
                </button>
                <button
                  onClick={() => handleSectionSave('title', latestArticlesContent.title)}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                >
                  Save Title
                </button>
                <button
                  onClick={() => handleSectionSave('description', latestArticlesContent.description)}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                >
                  Save Description
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Badge Display */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-3">Current Badge</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <div className="inline-block px-4 py-1 rounded-full bg-[#00C2FF]/20 backdrop-blur-sm text-[#00C2FF] text-sm font-medium">
                      {latestArticlesContent.badge.en}
                    </div>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <div className="inline-block px-4 py-1 rounded-full bg-[#00C2FF]/20 backdrop-blur-sm text-[#00C2FF] text-sm font-medium text-right" dir="rtl">
                      {latestArticlesContent.badge.ar}
                    </div>
                  </div>
                </div>
              </div>

              {/* Title Display */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-3">Current Title</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <div className="bg-gray-700 p-3 rounded">
                      <h2 className="text-3xl md:text-4xl font-bold text-white relative">
                        {latestArticlesContent.title.en}
                        <span className="absolute bottom-1 start-0 w-full h-[2px] bg-gradient-to-r from-[#00C2FF] to-[#8B5CF6]"></span>
                      </h2>
                    </div>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <div className="bg-gray-700 p-3 rounded text-right" dir="rtl">
                      <h2 className="text-3xl md:text-4xl font-bold text-white relative">
                        {latestArticlesContent.title.ar}
                        <span className="absolute bottom-1 start-0 w-full h-[2px] bg-gradient-to-r from-[#00C2FF] to-[#8B5CF6]"></span>
                      </h2>
                    </div>
                  </div>
                </div>
              </div>

              {/* Description Display */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-3">Current Description</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <p className="text-gray-300 bg-gray-700 p-3 rounded text-lg">{latestArticlesContent.description.en}</p>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <p className="text-gray-300 bg-gray-700 p-3 rounded text-lg text-right" dir="rtl">{latestArticlesContent.description.ar}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Live Preview Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-white flex items-center mb-4">
            <FiEye className="mr-2 h-5 w-5 text-[#00C2FF]" />
            Live Preview
          </h2>
          <div className="bg-gray-900 rounded-lg p-8 border border-gray-600">
            <div className="text-center mb-12">
              <div className="inline-block px-4 py-1 rounded-full bg-[#00C2FF]/20 backdrop-blur-sm text-[#00C2FF] text-sm font-medium mb-4">
                {latestArticlesContent.badge.en}
              </div>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                <span className="relative text-white">
                  {latestArticlesContent.title.en}
                  <span className="absolute bottom-1 start-0 w-full h-[2px] bg-gradient-to-r from-[#00C2FF] to-[#8B5CF6]"></span>
                </span>
              </h2>
              <p className="max-w-3xl mx-auto text-gray-300 text-lg">
                {latestArticlesContent.description.en}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}