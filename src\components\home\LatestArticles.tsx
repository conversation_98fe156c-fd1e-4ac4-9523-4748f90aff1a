"use client";

import { useEffect, useRef, useState } from "react";
import Link from "next/link";
import { motion, useAnimation } from "framer-motion";
import ArticlesBackground from "./ArticlesBackground";
import { useLanguage } from '@/contexts/LanguageContext';

// Interface for the section content from API
interface LatestArticlesSectionContent {
  id: number;
  badge: string;
  title: string;
  description: string;
  created_at: string;
  updated_at: string;
}

// Latest 3 articles from the mock data
const latestArticles = [
  {
    id: 1,
    title: "The Future of Urban Development in the UAE",
    excerpt: "Exploring innovative approaches to urban planning and sustainable development in the UAE's growing cities.",
    date: "June 20, 2023",
    category: "Urban Planning",
    image: "/images/article-1.jpg",
    slug: "future-urban-development-uae",
  },
  {
    id: 7,
    title: "Luxury Real Estate Trends in Dubai for 2024",
    excerpt: "An insider look at the evolving luxury property market in Dubai and what high-net-worth investors are seeking today.",
    date: "July 12, 2023",
    category: "Investment",
    image: "/images/article-7.jpg",
    slug: "luxury-real-estate-trends-dubai-2024",
  },
  {
    id: 9,
    title: "PropTech Revolution: How Technology is Transforming Real Estate",
    excerpt: "From AI property management to blockchain transactions, discover how technology is revolutionizing every aspect of real estate.",
    date: "September 18, 2023",
    category: "Technology",
    image: "/images/article-9.jpg",
    slug: "proptech-revolution-technology-real-estate",
  },
];

// Interactive border effect for cards
const InteractiveBorder = ({ children }: { children: React.ReactNode }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const relativeX = x / rect.width;
    const relativeY = y / rect.height;

    setMousePosition({ x: relativeX, y: relativeY });
  };

  return (
    <div
      ref={containerRef}
      className="relative h-full rounded-xl group"
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      {/* Radial gradient that follows mouse */}
      <div className="absolute inset-0 overflow-hidden rounded-xl pointer-events-none">
        <div
          className="absolute w-[250px] h-[250px] rounded-full bg-gradient-to-r from-[rgb(var(--color-primary))]/20 to-[rgb(var(--color-secondary))]/20 blur-xl opacity-0 transition-opacity duration-300"
          style={{
            opacity: isHovering ? 0.8 : 0,
            left: `calc(${mousePosition.x * 100}% - 125px)`,
            top: `calc(${mousePosition.y * 100}% - 125px)`,
            transform: 'translateZ(0)',
          }}
        />
      </div>

      {/* Border effects that follow mouse */}
      <div className="absolute inset-0 rounded-xl overflow-hidden pointer-events-none z-10">
        {/* Top border */}
        <div
          className="absolute top-0 start-0 w-[45%] h-[3px] bg-gradient-to-r from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] opacity-0 transition-all duration-300"
          style={{
            opacity: isHovering ? 1 : 0,
            transform: `translateX(${mousePosition.x * 110}%)`,
            width: '45%',
            left: `${Math.min(Math.max(mousePosition.x * 100 - 22.5, 0), 55)}%`,
            boxShadow: '0 0 15px 2px rgb(var(--color-primary))',
          }}
        />

        {/* Right border */}
        <div
          className="absolute top-0 end-0 w-[3px] h-[45%] bg-gradient-to-b from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] opacity-0 transition-all duration-300"
          style={{
            opacity: isHovering ? 1 : 0,
            transform: `translateY(${mousePosition.y * 110}%)`,
            height: '45%',
            top: `${Math.min(Math.max(mousePosition.y * 100 - 22.5, 0), 55)}%`,
            boxShadow: '0 0 15px 2px rgb(var(--color-primary))',
          }}
        />

        {/* Bottom border */}
        <div
          className="absolute bottom-0 end-0 w-[45%] h-[3px] bg-gradient-to-l from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] opacity-0 transition-all duration-300"
          style={{
            opacity: isHovering ? 1 : 0,
            transform: `translateX(-${mousePosition.x * 110}%)`,
            width: '45%',
            right: `${Math.min(Math.max((1-mousePosition.x) * 100 - 22.5, 0), 55)}%`,
            boxShadow: '0 0 15px 2px rgb(var(--color-secondary))',
          }}
        />

        {/* Left border */}
        <div
          className="absolute bottom-0 start-0 w-[3px] h-[45%] bg-gradient-to-t from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] opacity-0 transition-all duration-300"
          style={{
            opacity: isHovering ? 1 : 0,
            transform: `translateY(-${mousePosition.y * 110}%)`,
            height: '45%',
            bottom: `${Math.min(Math.max((1-mousePosition.y) * 100 - 22.5, 0), 55)}%`,
            boxShadow: '0 0 15px 2px rgb(var(--color-secondary))',
          }}
        />
      </div>

      {/* Ambient inner glow */}
      <div className="absolute inset-[2px] rounded-lg bg-gradient-to-br from-[rgb(var(--color-primary))]/10 to-[rgb(var(--color-secondary))]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none blur-sm z-0"></div>

      {children}
    </div>
  );
};

const LatestArticles = () => {
  const { locale } = useLanguage();
  const controls = useAnimation();
  const sectionRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [sectionContent, setSectionContent] = useState<LatestArticlesSectionContent | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch section content from API - following Hero.tsx pattern
  useEffect(() => {
    const fetchSectionContent = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';
        const endpoint = `/api/home-page/latest-articles/${locale}/`;

        console.log('🔄 Fetching latest articles section content from:', `${apiBaseUrl}${endpoint}`);

        const response = await fetch(`${apiBaseUrl}${endpoint}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const data = await response.json();
        console.log('📥 Latest articles section response:', data);

        if (data.success && data.data) {
          setSectionContent(data.data);
        } else {
          console.error('Failed to fetch latest articles section content:', data.message);
          // Keep sectionContent as null to fall back to default content
        }
      } catch (error) {
        console.error('Error fetching latest articles section content:', error);
        // Keep sectionContent as null to fall back to default content
      } finally {
        setIsLoading(false);
      }
    };

    fetchSectionContent();
  }, [locale]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    if (isVisible) {
      controls.start(i => ({
        opacity: 1,
        y: 0,
        transition: { delay: i * 0.2, duration: 0.5 }
      }));
    }
  }, [isVisible, controls]);

  return (
    <section ref={sectionRef} className="py-16 relative overflow-hidden">
      {/* Add the articles/architecture background */}
      <ArticlesBackground />

      <div className="container mx-auto px-4 relative z-10">
        {/* Section header */}
        <div className="text-center mb-12">
          <div className="inline-block px-4 py-1 rounded-full bg-[rgb(var(--color-primary))]/20 backdrop-blur-sm text-[rgb(var(--color-primary))] text-sm font-medium mb-4">
            {sectionContent?.badge || "Latest Insights"}
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            <span className="relative">
              {sectionContent?.title || "Latest Articles"}
              <span className="absolute bottom-1 start-0 w-full h-[2px] bg-gradient-to-r from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))]"></span>
            </span>
          </h2>
          <p className="max-w-3xl mx-auto text-[rgb(var(--color-text-secondary))] text-lg">
            {sectionContent?.description || "Stay updated with our latest news and insights in real estate development"}
          </p>
        </div>

        {/* Articles grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {latestArticles.map((article, index) => (
            <motion.div
              key={article.id}
              custom={index}
              initial={{ opacity: 0, y: 30 }}
              animate={controls}
            >
              <Link href={`/articles/${article.slug}`} className="block h-full">
                <InteractiveBorder>
                  <div className="group h-full bg-[rgb(var(--color-text))]/5 backdrop-blur-sm border border-[rgb(var(--color-text))]/10 hover:border-[rgb(var(--color-primary))]/70 hover:border-2 hover:border-opacity-90 rounded-xl overflow-hidden transition-all duration-500 hover:shadow-lg hover:shadow-[rgb(var(--color-primary))]/20 hover:scale-[1.02] relative flex flex-col hover:[box-shadow:0_0_10px_1px_rgb(var(--color-primary))]">
                    {/* Article image */}
                    <div className="relative w-full aspect-[16/9] overflow-hidden">
                      {/* Category badge */}
                      <div className="absolute top-3 start-3 z-10">
                        <span className="inline-block px-3 py-1 text-xs font-medium rounded-full bg-[rgb(var(--color-primary))]/80 text-white backdrop-blur-sm">
                          {article.category}
                        </span>
                      </div>

                      {/* Placeholder for article image */}
                      <div className="absolute inset-0 bg-[#1a1a2e] flex items-center justify-center transform group-hover:scale-105 transition-transform duration-700 ease-in-out">
                        <span className="text-[rgb(var(--color-text))]/70">{article.title.substring(0, 20)}...</span>
                      </div>

                      {/* Gradient overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-[#0a0f23] via-transparent to-transparent opacity-60"></div>
                    </div>

                    {/* Article content */}
                    <div className="p-4 flex flex-col flex-grow">
                      <h3 className="text-lg font-bold mb-2 line-clamp-2 group-hover:text-[rgb(var(--color-primary))] transition-all">
                        {article.title}
                      </h3>

                      <div className="text-[rgb(var(--color-text))]/70 text-xs mb-2 flex items-center">
                        <svg className="w-3 h-3 me-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        {article.date}
                      </div>

                      <p className="text-[rgb(var(--color-text))]/70 text-sm mb-3 line-clamp-3 flex-grow">
                        {article.excerpt}
                      </p>

                      <div className="mt-auto pt-2 border-t border-[rgb(var(--color-text))]/10">
                        <span className="inline-flex items-center bg-white/10 hover:bg-white/20 text-white px-3 py-1 rounded-full border border-white/30 text-xs transition-all duration-300 font-medium">
                          READ MORE
                          <svg className="ms-1 h-3 w-3 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                          </svg>
                        </span>
                      </div>

                      {/* Decorative corner dot */}
                      <div className="absolute bottom-3 end-3 w-2 h-2 rounded-full bg-[rgb(var(--color-primary))]/60 group-hover:bg-[rgb(var(--color-primary))] transition-all"></div>
                    </div>
                  </div>
                </InteractiveBorder>
              </Link>
            </motion.div>
          ))}
        </div>

        {/* View All Articles button */}
        <div className="text-center mt-12">
          <Link
            href="/articles"
            className="group relative inline-flex items-center justify-center overflow-hidden rounded-full border border-transparent bg-white px-6 py-3 text-base font-medium text-gray-900 transition hover:scale-105"
          >
            <span>View All Articles</span>
            <svg className="ms-2 w-4 h-4 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default LatestArticles;