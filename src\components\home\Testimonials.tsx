"use client";

import { useState, useEffect, useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";

// Static Cityscape Background component
const TestimonialsBackground = () => {
  const [stars, setStars] = useState<{ key: number; size: number; top: number; start: number; delay: number; duration: number }[]>([]);
  const [isHydrated, setIsHydrated] = useState(false);
  
  // Generate stars on client-side only and set hydration flag
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const generateStars = () => {
        const newStars = [];
        for (let i = 0; i < 75; i++) {
          newStars.push({
            key: i,
            size: Math.random() * 2 + 0.5,
            top: Math.random() * 100,
            start: Math.random() * 100,
            delay: Math.random() * 5,
            duration: Math.floor(Math.random() * 4) + 3
          });
        }
        setStars(newStars);
        setIsHydrated(true);
      };
      
      generateStars();
    }
  }, []);
  
  return (
    <div className="absolute inset-0 w-full h-full overflow-hidden -z-10">
      {/* Deep gradient background with enhanced colors */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#0a0e29] via-[#0b1233] to-[#121f52] z-[-5]"></div>
      
      {/* Animated gradient overlay */}
      <div className="absolute inset-0 opacity-70 z-[-4]">
        <div className="absolute top-0 -start-1/4 w-[600px] h-[600px] bg-gradient-to-r from-indigo-900/60 to-transparent rounded-full filter blur-[120px] animate-pulse-slow"></div>
        <div className="absolute bottom-0 -end-1/4 w-[600px] h-[600px] bg-gradient-to-l from-purple-900/60 to-transparent rounded-full filter blur-[120px] animate-pulse-slow" style={{ animationDelay: '2s' }}></div>
      </div>
      
      {/* Engineering tools decorative elements */}
      <div className="absolute inset-0 opacity-40 z-[2]">
        {/* Compass */}
        <svg className="absolute top-[5%] start-[10%] w-64 h-64 text-indigo-200 opacity-40 rotate-[20deg] transform-gpu" viewBox="0 0 100 100" fill="none" stroke="currentColor" strokeWidth="1.5">
          <circle cx="50" cy="50" r="40" filter="drop-shadow(0 0 2px rgba(99, 102, 241, 0.5))" />
          <circle cx="50" cy="50" r="2" fill="currentColor" />
          <path d="M50 50 L50 10" strokeWidth="2" stroke="rgba(99, 102, 241, 0.8)" />
          <path d="M50 50 L90 50" strokeWidth="2" stroke="rgba(139, 92, 246, 0.8)" />
          <path d="M50 10 L45 15 M50 10 L55 15" strokeWidth="1" />
          <circle cx="50" cy="50" r="5" strokeWidth="1" fill="transparent" />
          <circle cx="50" cy="50" r="44" strokeWidth="0.5" strokeDasharray="4 2" stroke="rgba(139, 92, 246, 0.4)" />
        </svg>
        
        {/* Ruler */}
        <svg className="absolute top-[15%] end-[5%] w-80 h-28 text-indigo-200 opacity-40 rotate-[-15deg] transform-gpu" viewBox="0 0 200 40" fill="none" stroke="currentColor" strokeWidth="1.2">
          <rect x="5" y="5" width="190" height="30" filter="drop-shadow(0 0 3px rgba(99, 102, 241, 0.3))" />
          {Array.from({ length: 20 }).map((_, i) => (
            <line 
              key={`ruler-mark-${i}`}
              x1={i * 10 + 5} 
              y1="5" 
              x2={i * 10 + 5} 
              y2={i % 5 === 0 ? 25 : (i % 2 === 0 ? 20 : 15)}
              strokeWidth={i % 5 === 0 ? 1.5 : 1}
              stroke={i % 2 === 0 ? "rgba(139, 92, 246, 0.8)" : "currentColor"}
            />
          ))}
          {Array.from({ length: 4 }).map((_, i) => (
            <text 
              key={`ruler-text-${i}`}
              x={i * 50 + 5} 
              y="18"
              fill="rgba(139, 92, 246, 0.7)"
              fontSize="6"
              textAnchor="middle"
            >{i * 5}</text>
          ))}
        </svg>
        
        {/* Triangle */}
        <svg className="absolute bottom-[25%] start-[8%] w-60 h-60 text-indigo-200 opacity-40 rotate-[30deg] transform-gpu" viewBox="0 0 100 100" fill="none" stroke="currentColor" strokeWidth="1.5">
          <path d="M10 80 L90 80 L50 20 Z" strokeWidth="2" filter="drop-shadow(0 0 3px rgba(99, 102, 241, 0.3))" />
          <path d="M50 80 L50 20" strokeWidth="1" strokeDasharray="3 2" stroke="rgba(139, 92, 246, 0.7)" />
          <path d="M50 50 L90 80" strokeWidth="1" strokeDasharray="3 2" stroke="rgba(139, 92, 246, 0.7)" />
          <text x="45" y="55" fill="rgba(139, 92, 246, 0.8)" fontSize="5">30°</text>
          <text x="65" y="75" fill="rgba(139, 92, 246, 0.8)" fontSize="5">60°</text>
        </svg>
        
        {/* Protractor */}
        <svg className="absolute bottom-[15%] end-[10%] w-72 h-72 text-indigo-200 opacity-40 rotate-[5deg] transform-gpu" viewBox="0 0 100 100" fill="none" stroke="currentColor" strokeWidth="1.5">
          <path d="M10 50 L90 50" strokeWidth="1" />
          <path d="M50 10 L50 50" strokeWidth="1" />
          <path d="M50 50 A 40 40 0 0 1 90 50" strokeWidth="2" stroke="rgba(139, 92, 246, 0.8)" filter="drop-shadow(0 0 2px rgba(99, 102, 241, 0.5))" />
          {Array.from({ length: 9 }).map((_, i) => (
            <path 
              key={`angle-${i}`}
              d={`M50 50 L${50 + 40 * Math.cos(Math.PI * i / 8)} ${50 - 40 * Math.sin(Math.PI * i / 8)}`}
              strokeWidth="0.8"
              stroke={i % 2 === 0 ? "rgba(139, 92, 246, 0.7)" : "rgba(99, 102, 241, 0.6)"}
            />
          ))}
          {Array.from({ length: 5 }).map((_, i) => (
            <text 
              key={`angle-text-${i}`}
              x={50 + 30 * Math.cos(Math.PI * i / 4)} 
              y={50 - 30 * Math.sin(Math.PI * i / 4) + 3}
              fill="rgba(139, 92, 246, 0.8)"
              fontSize="4"
              textAnchor="middle"
            >{i * 45}°</text>
          ))}
        </svg>
        
        {/* T-Square */}
        <svg className="absolute top-[50%] end-[18%] w-80 h-60 text-indigo-200 opacity-40 rotate-[-10deg] transform-gpu" viewBox="0 0 120 80" fill="none" stroke="currentColor" strokeWidth="1.5">
          <path d="M10 30 L110 30" strokeWidth="2" stroke="rgba(139, 92, 246, 0.8)" filter="drop-shadow(0 0 2px rgba(99, 102, 241, 0.5))" />
          <path d="M30 5 L30 75" strokeWidth="2" stroke="rgba(99, 102, 241, 0.8)" filter="drop-shadow(0 0 2px rgba(99, 102, 241, 0.5))" />
          <rect x="25" y="25" width="10" height="10" strokeWidth="1.5" />
          {Array.from({ length: 10 }).map((_, i) => (
            <line 
              key={`tsq-mark-h-${i}`}
              x1={i * 10 + 35} 
              y1="28" 
              x2={i * 10 + 35} 
              y2="32"
              strokeWidth="0.8"
              stroke="rgba(139, 92, 246, 0.7)"
            />
          ))}
        </svg>
        
        {/* Drafting Tools Set */}
        <svg className="absolute top-[45%] start-[20%] w-72 h-72 text-indigo-200 opacity-40 rotate-[15deg] transform-gpu" viewBox="0 0 100 100" fill="none" stroke="currentColor" strokeWidth="1.2">
          <circle cx="50" cy="50" r="30" strokeWidth="0.5" strokeDasharray="3 1" stroke="rgba(139, 92, 246, 0.4)" />
          <circle cx="50" cy="50" r="20" strokeWidth="0.5" strokeDasharray="2 2" stroke="rgba(99, 102, 241, 0.4)" />
          <path d="M20 50 L80 50" strokeWidth="1" stroke="rgba(139, 92, 246, 0.6)" />
          <path d="M50 20 L50 80" strokeWidth="1" stroke="rgba(139, 92, 246, 0.6)" />
          <path d="M30 30 L70 70" strokeWidth="0.8" strokeDasharray="2 1" stroke="rgba(99, 102, 241, 0.5)" />
          <path d="M30 70 L70 30" strokeWidth="0.8" strokeDasharray="2 1" stroke="rgba(99, 102, 241, 0.5)" />
          <circle cx="50" cy="50" r="5" strokeWidth="1" fill="transparent" stroke="rgba(139, 92, 246, 0.8)" />
          <circle cx="50" cy="50" r="2" fill="rgba(139, 92, 246, 0.8)" />
        </svg>
        
        {/* Blueprint Grid - More visible */}
        <div className="absolute inset-0">
          {Array.from({ length: 20 }).map((_, i) => (
            <div 
              key={`h-line-${i}`}
              className="absolute w-full h-[0.5px] bg-indigo-400/20"
              style={{ top: `${(i + 1) * 5}%` }}
            ></div>
          ))}
          {Array.from({ length: 20 }).map((_, i) => (
            <div 
              key={`v-line-${i}`}
              className="absolute h-full w-[0.5px] bg-indigo-400/20"
              style={{ left: `${(i + 1) * 5}%` }}
            ></div>
          ))}
        </div>
        {/* Measurements and Dimensions */}
        <svg className="absolute top-[35%] start-[40%] w-96 h-96 text-indigo-200 opacity-40 transform-gpu" viewBox="0 0 100 100" fill="none" stroke="currentColor" strokeWidth="1.2">
          <rect x="20" y="20" width="60" height="60" strokeWidth="1.5" strokeDasharray="5 2" stroke="rgba(139, 92, 246, 0.7)" filter="drop-shadow(0 0 2px rgba(99, 102, 241, 0.3))" />
          <path d="M20 15 L80 15" strokeWidth="1" />
          <path d="M15 20 L15 80" strokeWidth="1" />
          <path d="M20 15 L20 17" strokeWidth="1" />
          <path d="M80 15 L80 17" strokeWidth="1" />
          <path d="M15 20 L17 20" strokeWidth="1" />
          <path d="M15 80 L17 80" strokeWidth="1" />
          <text x="50" y="13" fontSize="4" textAnchor="middle" fill="rgba(139, 92, 246, 0.9)">60 cm</text>
          <text x="13" y="50" fontSize="4" textAnchor="middle" fill="rgba(139, 92, 246, 0.9)" transform="rotate(-90, 13, 50)">60 cm</text>
          
          {/* Additional Dimension Lines */}
          <path d="M40 20 L40 15" strokeWidth="0.7" stroke="rgba(99, 102, 241, 0.6)" />
          <path d="M60 20 L60 15" strokeWidth="0.7" stroke="rgba(99, 102, 241, 0.6)" />
          <path d="M20 40 L15 40" strokeWidth="0.7" stroke="rgba(99, 102, 241, 0.6)" />
          <path d="M20 60 L15 60" strokeWidth="0.7" stroke="rgba(99, 102, 241, 0.6)" />
        </svg>
        
        {/* Additional Decorative Element - Circular Blueprint */}
        <svg className="absolute bottom-[45%] start-[60%] w-64 h-64 text-indigo-200 opacity-30 transform-gpu" viewBox="0 0 100 100" fill="none" stroke="currentColor">
          <circle cx="50" cy="50" r="45" strokeWidth="0.8" stroke="rgba(99, 102, 241, 0.4)" />
          <circle cx="50" cy="50" r="35" strokeWidth="0.8" stroke="rgba(139, 92, 246, 0.4)" strokeDasharray="4 2" />
          <circle cx="50" cy="50" r="25" strokeWidth="0.8" stroke="rgba(99, 102, 241, 0.4)" />
          <circle cx="50" cy="50" r="15" strokeWidth="0.8" stroke="rgba(139, 92, 246, 0.4)" strokeDasharray="4 2" />
          <circle cx="50" cy="50" r="5" strokeWidth="0.8" stroke="rgba(99, 102, 241, 0.4)" />
          <path d="M5 50 L95 50" strokeWidth="0.6" stroke="rgba(139, 92, 246, 0.4)" strokeDasharray="2 1" />
          <path d="M50 5 L50 95" strokeWidth="0.6" stroke="rgba(139, 92, 246, 0.4)" strokeDasharray="2 1" />
          <path d="M15 15 L85 85" strokeWidth="0.6" stroke="rgba(99, 102, 241, 0.4)" strokeDasharray="2 1" />
          <path d="M15 85 L85 15" strokeWidth="0.6" stroke="rgba(99, 102, 241, 0.4)" strokeDasharray="2 1" />
        </svg>
      </div>
      
      {/* Stars effect - client-side only rendering */}
      {isHydrated && (
        <div className="star-field">
          {stars.map((star) => (
            <div
              key={`star-${star.key}`}
              className="star"
              style={{
                width: `${star.size}px`,
                height: `${star.size}px`,
                top: `${star.top}%`,
                left: `${star.start}%`,
                animationDuration: `${star.duration}s`,
                animationDelay: `${star.delay}s`
              }}
            />
          ))}
        </div>
      )}
      
      {/* Static cityscape silhouette with enhanced colors */}
      <div className="absolute bottom-0 start-0 w-full">
        <svg
          viewBox="0 0 1200 300"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          preserveAspectRatio="none"
          className="w-full h-auto"
        >
          {/* City Buildings - Rear Layer */}
          <path
            d="M0,300 L0,200 L50,190 L50,170 L70,170 L70,150 L90,150 L90,170 L110,170 L110,190 
                L150,190 L150,160 L170,160 L170,140 L190,140 L190,120 L210,120 L210,160 L230,160 
                L230,180 L280,180 L280,170 L320,170 L320,190 L350,190 L350,160 L370,160 L370,140 
                L390,140 L390,180 L410,180 L410,200 L450,200 L450,170 L500,170 L500,190 L550,190 
                L550,220 L650,220 L650,180 L700,180 L700,200 L720,200 L720,170 L750,170 L750,150 
                L770,150 L770,180 L800,180 L800,200 L850,200 L850,170 L900,170 L900,190 L950,190 
                L950,180 L1000,180 L1000,200 L1050,200 L1050,160 L1100,160 L1100,180 L1150,180 
                L1150,240 L1200,240 L1200,300 Z"
            fill="#0f1a4a"
            className="opacity-80"
          />
          
          {/* City Buildings - Front Layer */}
          <path
            d="M0,300 L0,220 L30,220 L30,180 L80,180 L80,220 L100,220 L100,200 L130,200 L130,180 
                L160,180 L160,160 L180,160 L180,140 L200,140 L200,240 L220,240 L220,210 L240,210 
                L240,190 L260,190 L260,210 L280,210 L280,190 L300,190 L300,220 L320,220 L320,200 
                L340,200 L340,220 L360,220 L360,180 L400,180 L400,250 L430,250 L430,230 L460,230 
                L460,210 L480,210 L480,190 L500,190 L500,210 L530,210 L530,230 L560,230 L560,260 
                L580,260 L580,240 L600,240 L600,210 L640,210 L640,250 L660,250 L660,230 L690,230 
                L690,210 L710,210 L710,190 L750,190 L750,230 L780,230 L780,250 L800,250 L800,230 
                L830,230 L830,200 L860,200 L860,230 L890,230 L890,210 L920,210 L920,240 L950,240 
                L950,220 L980,220 L980,200 L1000,200 L1000,180 L1020,180 L1020,230 L1050,230 L1050,250 
                L1070,250 L1070,230 L1090,230 L1090,200 L1120,200 L1120,250 L1170,250 L1170,220 
                L1200,220 L1200,300 Z"
            fill="#061130"
            className="opacity-90"
          />
          
          {/* Windows as glowing dots */}
          <g className="opacity-70">
            <circle cx="90" cy="160" r="2" fill="#4f6bff" className="animate-pulse-slow" style={{ animationDelay: '0.5s' }} />
            <circle cx="210" cy="150" r="2" fill="#4f6bff" className="animate-pulse-slow" style={{ animationDelay: '1.2s' }} />
            <circle cx="370" cy="150" r="2" fill="#4f6bff" className="animate-pulse-slow" style={{ animationDelay: '0.7s' }} />
            <circle cx="480" cy="200" r="2" fill="#4f6bff" className="animate-pulse-slow" style={{ animationDelay: '1.5s' }} />
            <circle cx="580" cy="230" r="2" fill="#4f6bff" className="animate-pulse-slow" style={{ animationDelay: '2.1s' }} />
            <circle cx="700" cy="190" r="2" fill="#4f6bff" className="animate-pulse-slow" style={{ animationDelay: '0.3s' }} />
            <circle cx="780" cy="240" r="2" fill="#4f6bff" className="animate-pulse-slow" style={{ animationDelay: '1.1s' }} />
            <circle cx="890" cy="220" r="2" fill="#4f6bff" className="animate-pulse-slow" style={{ animationDelay: '1.8s' }} />
            <circle cx="1020" cy="200" r="2" fill="#4f6bff" className="animate-pulse-slow" style={{ animationDelay: '0.9s' }} />
            <circle cx="1100" cy="210" r="2" fill="#4f6bff" className="animate-pulse-slow" style={{ animationDelay: '1.4s' }} />
            <circle cx="150" cy="170" r="2" fill="#a364ff" className="animate-pulse-slow" style={{ animationDelay: '0.2s' }} />
            <circle cx="250" cy="200" r="2" fill="#a364ff" className="animate-pulse-slow" style={{ animationDelay: '1.7s' }} />
            <circle cx="390" cy="170" r="2" fill="#a364ff" className="animate-pulse-slow" style={{ animationDelay: '1.0s' }} />
            <circle cx="510" cy="200" r="2" fill="#a364ff" className="animate-pulse-slow" style={{ animationDelay: '0.8s' }} />
            <circle cx="640" cy="230" r="2" fill="#a364ff" className="animate-pulse-slow" style={{ animationDelay: '1.3s' }} />
            <circle cx="740" cy="210" r="2" fill="#a364ff" className="animate-pulse-slow" style={{ animationDelay: '2.0s' }} />
            <circle cx="850" cy="210" r="2" fill="#a364ff" className="animate-pulse-slow" style={{ animationDelay: '0.4s' }} />
            <circle cx="950" cy="230" r="2" fill="#a364ff" className="animate-pulse-slow" style={{ animationDelay: '1.6s' }} />
            <circle cx="1050" cy="240" r="2" fill="#a364ff" className="animate-pulse-slow" style={{ animationDelay: '0.6s' }} />
            <circle cx="1140" cy="240" r="2" fill="#a364ff" className="animate-pulse-slow" style={{ animationDelay: '1.9s' }} />
          </g>
          
          {/* Glow effect around some buildings */}
          <path
            d="M400,250 L400,180 L360,180 L360,220 L340,220 L340,200 L320,220 L320,200 L300,190 L300,220 L240,210 L240,190 L260,190 L260,210 L280,210"
            stroke="rgba(79, 107, 255, 0.1)"
            strokeWidth="2"
            fill="none"
            className="opacity-60"
          />
          <path
            d="M700,200 L650,180 L650,220 L550,220 L550,190 L500,190 L500,170 L450,170 L450,200 L410,200"
            stroke="rgba(163, 100, 255, 0.1)"
            strokeWidth="2"
            fill="none"
            className="opacity-60"
          />
        </svg>
      </div>
      
      {/* Subtle floating quote marks as decoration - restyled */}
      <div className="absolute top-10 start-[10%] text-[120px] font-serif text-indigo-500/10 opacity-80">
        "
      </div>
      <div className="absolute bottom-20 end-[15%] text-[120px] font-serif text-purple-500/10 opacity-80">
        "
      </div>
      
      {/* Enhanced glow effects */}
      <div className="absolute top-[30%] start-[20%] w-64 h-64 rounded-full bg-indigo-500/10 blur-3xl"></div>
      <div className="absolute bottom-[40%] end-[25%] w-72 h-72 rounded-full bg-purple-500/10 blur-3xl"></div>
      
      {/* Architectural grid overlay */}
      <div className="absolute inset-0 z-10 pointer-events-none opacity-30">
        <div className="h-full w-full grid grid-cols-6 lg:grid-cols-12">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="border-s border-white/5 h-full">
              {i === 0 && <div className="border-e border-white/5 h-full w-full"></div>}
            </div>
          ))}
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i + 'row'} className="col-span-full border-t border-white/5 h-0"></div>
          ))}
        </div>
      </div>
      
      {/* Highlight beam effect */}
      <div className="absolute start-1/4 -top-20 w-1 h-[150%] bg-gradient-to-b from-transparent via-indigo-500/10 to-transparent rotate-[20deg] blur-sm"></div>
      <div className="absolute end-1/3 -top-20 w-1 h-[150%] bg-gradient-to-b from-transparent via-purple-500/10 to-transparent -rotate-[15deg] blur-sm"></div>
    </div>
  );
};

// Component with bright engineering background elements
const EngineeringDecorations = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none z-10 opacity-30">
      {/* Large distinct blueprint grid in bright purple/blue */}
      <div className="absolute inset-0 opacity-60">
        {Array.from({ length: 15 }).map((_, i) => (
          <div 
            key={`h-grid-${i}`}
            className="absolute w-full h-[1px] bg-indigo-400/40"
            style={{ top: `${(i + 1) * 6.66}%` }}
          ></div>
        ))}
        {Array.from({ length: 15 }).map((_, i) => (
          <div 
            key={`v-grid-${i}`}
            className="absolute h-full w-[1px] bg-indigo-400/40"
            style={{ left: `${(i + 1) * 6.66}%` }}
          ></div>
        ))}
      </div>

      {/* Large Architectural Drawing - Highly Visible */}
      <div className="absolute inset-0">
        {/* Large central blueprint with bright colors */}
        <svg className="absolute top-[20%] end-[20%] w-[800px] h-[600px]" viewBox="0 0 800 600">
          <rect x="150" y="100" width="500" height="400" fill="none" stroke="#6366F1" strokeWidth="2" strokeDasharray="10 5" />
          <line x1="150" y1="100" x2="650" y2="100" stroke="#A78BFA" strokeWidth="2" />
          <line x1="150" y1="500" x2="650" y2="500" stroke="#A78BFA" strokeWidth="2" />
          <line x1="150" y1="100" x2="150" y2="500" stroke="#A78BFA" strokeWidth="2" />
          <line x1="650" y1="100" x2="650" y2="500" stroke="#A78BFA" strokeWidth="2" />
          
          {/* Inner room lines */}
          <line x1="150" y1="200" x2="650" y2="200" stroke="#8B5CF6" strokeWidth="1.5" strokeDasharray="8 4" />
          <line x1="150" y1="350" x2="650" y2="350" stroke="#8B5CF6" strokeWidth="1.5" strokeDasharray="8 4" />
          <line x1="350" y1="100" x2="350" y2="500" stroke="#8B5CF6" strokeWidth="1.5" strokeDasharray="8 4" />
          <line x1="500" y1="100" x2="500" y2="500" stroke="#8B5CF6" strokeWidth="1.5" strokeDasharray="8 4" />
          
          {/* Dimensions */}
          <line x1="150" y1="80" x2="650" y2="80" stroke="#A78BFA" strokeWidth="1" />
          <line x1="150" y1="80" x2="150" y2="70" stroke="#A78BFA" strokeWidth="1" />
          <line x1="650" y1="80" x2="650" y2="70" stroke="#A78BFA" strokeWidth="1" />
          <text x="400" y="65" fill="#A78BFA" fontSize="16" textAnchor="middle">500</text>
          
          <line x1="130" y1="100" x2="130" y2="500" stroke="#A78BFA" strokeWidth="1" />
          <line x1="130" y1="100" x2="120" y2="100" stroke="#A78BFA" strokeWidth="1" />
          <line x1="130" y1="500" x2="120" y2="500" stroke="#A78BFA" strokeWidth="1" />
          <text x="100" y="300" fill="#A78BFA" fontSize="16" textAnchor="middle" transform="rotate(-90 100 300)">400</text>
          
          {/* Radial element */}
          <circle cx="350" cy="250" r="50" fill="none" stroke="#6366F1" strokeWidth="2" />
          <circle cx="350" cy="250" r="70" fill="none" stroke="#8B5CF6" strokeWidth="1" strokeDasharray="5 3" />
          <circle cx="350" cy="250" r="30" fill="none" stroke="#A78BFA" strokeWidth="1.5" />
          <line x1="280" y1="250" x2="420" y2="250" stroke="#6366F1" strokeWidth="1" />
          <line x1="350" y1="180" x2="350" y2="320" stroke="#6366F1" strokeWidth="1" />
          
          {/* Angle markings */}
          <path d="M350 250 L400 250 A50 50 0 0 0 375 207" fill="none" stroke="#A78BFA" strokeWidth="1.5" />
          <text x="385" y="235" fill="#A78BFA" fontSize="12">30°</text>
          
          {/* Additional details */}
          <rect x="400" y="400" width="100" height="50" fill="#6366F1" fillOpacity="0.2" stroke="#6366F1" strokeWidth="1.5" />
          <rect x="200" y="150" width="100" height="30" fill="#8B5CF6" fillOpacity="0.2" stroke="#8B5CF6" strokeWidth="1.5" />
          
          {/* Labels - Large and clearly visible */}
          <text x="250" y="170" fill="#FFFFFF" fontSize="14" textAnchor="middle">OFFICE</text>
          <text x="450" y="425" fill="#FFFFFF" fontSize="14" textAnchor="middle">CONFERENCE</text>
          <text x="525" y="300" fill="#A78BFA" fontSize="18">FLOOR PLAN</text>
          <text x="350" y="250" fill="#FFFFFF" fontSize="10" textAnchor="middle">CENTER</text>
        </svg>
        
        {/* Architectural tools - Very bright and visible */}
        <div className="absolute top-[10%] start-[5%] w-[250px] h-[250px] opacity-75">
          <svg viewBox="0 0 100 100" width="100%" height="100%">
            <circle cx="50" cy="50" r="40" fill="none" stroke="#818CF8" strokeWidth="3" />
            <line x1="50" y1="10" x2="50" y2="90" stroke="#818CF8" strokeWidth="2" />
            <line x1="10" y1="50" x2="90" y2="50" stroke="#818CF8" strokeWidth="2" />
            <text x="70" y="30" fill="#A78BFA" fontSize="8">90°</text>
            <circle cx="50" cy="50" r="5" fill="#A78BFA" />
          </svg>
        </div>
        
        <div className="absolute bottom-[15%] end-[5%] w-[200px] h-[150px] opacity-75">
          <svg viewBox="0 0 100 60" width="100%" height="100%">
            <polygon points="10,50 90,50 50,10" fill="none" stroke="#818CF8" strokeWidth="3" />
            <line x1="50" y1="10" x2="50" y2="50" stroke="#8B5CF6" strokeWidth="2" strokeDasharray="3 2" />
            <text x="45" y="35" fill="#A78BFA" fontSize="8">30°</text>
            <text x="70" y="40" fill="#A78BFA" fontSize="8">60°</text>
          </svg>
        </div>
        
        <div className="absolute top-[60%] start-[10%] w-[300px] h-[80px] opacity-75 rotate-12 transform-gpu">
          <svg viewBox="0 0 150 40" width="100%" height="100%">
            <rect x="5" y="5" width="140" height="30" fill="#6366F1" fillOpacity="0.15" stroke="#818CF8" strokeWidth="2" />
            {Array.from({ length: 14 }).map((_, i) => (
              <line 
                key={`ruler-${i}`}
                x1={i * 10 + 5} 
                y1="5" 
                x2={i * 10 + 5} 
                y2={i % 5 === 0 ? "35" : (i % 2 === 0 ? "25" : "15")}
                stroke="#A78BFA" 
                strokeWidth={i % 5 === 0 ? "2" : "1.5"} 
              />
            ))}
            {Array.from({ length: 3 }).map((_, i) => (
              <text 
                key={`ruler-num-${i}`}
                x={i * 50 + 5} 
                y="20"
                fill="#FFFFFF"
                fontSize="8"
                textAnchor="middle"
              >{i * 5}</text>
            ))}
          </svg>
        </div>
      </div>
      
      {/* Glowing Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-[40%] start-[40%] w-[15px] h-[15px] rounded-full bg-indigo-500 opacity-75 animate-pulse-slow"></div>
        <div className="absolute top-[60%] start-[60%] w-[10px] h-[10px] rounded-full bg-purple-500 opacity-75 animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-[30%] start-[70%] w-[12px] h-[12px] rounded-full bg-indigo-400 opacity-75 animate-pulse-slow" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-[70%] start-[30%] w-[8px] h-[8px] rounded-full bg-purple-400 opacity-75 animate-pulse-slow" style={{ animationDelay: '1.5s' }}></div>
      </div>
    </div>
  );
};

const Testimonials = () => {
  const testimonials = [
    {
      id: 1,
      name: "Ahmed Al Mansouri",
      position: "Property Investor",
      quote: "My investment with Mazaya Capital has consistently delivered above-market returns. Their professional team provides exceptional support and transparent communication throughout the investment process.",
      image: "/images/testimonial-1.jpg",
      rating: 5,
    },
    {
      id: 2,
      name: "Sarah Johnson",
      position: "Business Owner",
      quote: "Mazaya Business Park exceeded all our expectations. The strategic location, modern facilities, and excellent management have significantly enhanced our business operations and employee satisfaction.",
      image: "/images/testimonial-2.jpg",
      rating: 5,
    },
    {
      id: 3,
      name: "Mohammed Al Qasimi",
      position: "Homeowner",
      quote: "Purchasing a villa in Mazaya Villas was the best decision for my family. The quality of construction, attention to detail, and community amenities have made it an incredible place to live and raise children.",
      image: "/images/testimonial-3.jpg",
      rating: 5,
    },
  ];

  const [activeIndex, setActiveIndex] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);
  
  // Add parallax scrolling effect like in Hero
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });
  
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.6, 1, 1, 0.6]);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prev) => (prev === testimonials.length - 1 ? 0 : prev + 1));
    }, 6000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  // Handle manual navigation
  const goToSlide = (index: number) => {
    setActiveIndex(index);
  };

  const goToPrevSlide = () => {
    setActiveIndex((prev) => (prev === 0 ? testimonials.length - 1 : prev - 1));
  };

  const goToNextSlide = () => {
    setActiveIndex((prev) => (prev === testimonials.length - 1 ? 0 : prev + 1));
  };

  return (
    <section ref={sectionRef} className="relative min-h-screen flex items-center py-24 overflow-hidden bg-[#0A1429]">
      {/* Add the static cityscape background */}
      <TestimonialsBackground />
      
      {/* Add the new engineering decorations component */}
      <EngineeringDecorations />
      
      <motion.div style={{ opacity }} className="container mx-auto px-4 relative z-20">
        <div className="text-center mb-16">
          <div className="inline-block px-4 py-1 rounded-full bg-gradient-to-r from-indigo-500/20 to-purple-500/20 backdrop-blur-sm text-indigo-300 text-sm font-medium mb-4">
            Client Success Stories
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-4 text-white">
            <span className="relative inline-block">
              Client Testimonials
              <span className="absolute -bottom-2 start-0 w-full h-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500"></span>
            </span>
          </h2>
          <p className="text-indigo-200 max-w-3xl mx-auto text-lg mt-6">
            Hear what our satisfied clients say about their experience with Mazaya Capital
          </p>
        </div>

        <div className="max-w-6xl mx-auto relative">
          {/* Redesigned Testimonial Cards */}
          <div className="relative">
            {/* Cards */}
            <div
              className="flex transition-transform duration-700 ease-in-out"
              style={{ transform: `translateX(-${activeIndex * 100}%)` }}
            >
              {testimonials.map((testimonial) => (
                <div key={testimonial.id} className="min-w-full px-5">
                  <div className="relative backdrop-blur-2xl rounded-2xl overflow-hidden shadow-[0_8px_30px_rgb(0,0,0,0.6)] border border-indigo-500/40">
                    {/* Gradient background */}
                    <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/80 via-[#0A1429]/90 to-purple-900/70"></div>
                    
                    {/* Content */}
                    <div className="relative p-8 md:p-12">
                      <div className="flex flex-col md:flex-row items-center gap-8 md:gap-12">
                      {/* Left side - Client Image/Info */}
                        <div className="w-full md:w-1/3 flex flex-col items-center relative">
                          {/* Decorative circle behind avatar */}
                          <div className="absolute w-36 h-36 rounded-full bg-gradient-to-br from-indigo-500/10 to-purple-500/10 blur-md"></div>
                          
                          <div className="w-28 h-28 rounded-full bg-gradient-to-br from-indigo-400 to-purple-600 p-1 mb-6 shadow-lg shadow-indigo-500/20 relative z-10">
                            <div className="w-full h-full rounded-full bg-[#0a1029] flex items-center justify-center text-2xl font-bold text-white">
                            {testimonial.name.split(" ").map(n => n[0]).join("")}
                          </div>
                        </div>
                        
                          <h4 className="font-bold text-2xl text-white text-center mb-2 relative z-10">{testimonial.name}</h4>
                          <p className="text-indigo-300 text-center mb-4 text-lg relative z-10">{testimonial.position}</p>
                        
                          {/* Star Rating with gold gradient and decorative line moved right below them */}
                          <div className="flex flex-col items-center space-y-3 relative z-10">
                            <div className="flex space-x-1">
                          {[...Array(testimonial.rating)].map((_, i) => (
                                <svg key={i} className="w-6 h-6" viewBox="0 0 20 20">
                                  <defs>
                                    <linearGradient id={`star-gradient-${testimonial.id}-${i}`} x1="0%" y1="0%" x2="100%" y2="100%">
                                      <stop offset="0%" stopColor="#FFD700" />
                                      <stop offset="100%" stopColor="#FFA500" />
                                    </linearGradient>
                                  </defs>
                                  <path 
                                    fill={`url(#star-gradient-${testimonial.id}-${i})`} 
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" 
                                  />
                      </svg>
                          ))}
                            </div>
                            {/* Decorative line right under stars */}
                            <div className="w-24 h-1 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"></div>
                          </div>
                      </div>
                      
                      {/* Right side - Quote */}
                      <div className="w-full md:w-2/3 flex flex-col">
                          <div className="relative mb-6">
                            <svg className="h-16 w-16 text-indigo-400/20 absolute -top-8 -start-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9.58,10.58C9.86,11.13 9.46,11.8 8.83,11.8H5.91C5.5,11.8 5.16,11.46 5.16,11.05V8.95C5.16,7.05 6.71,5.5 8.61,5.5C9.73,5.5 10,4.69 10,4.69C10,4.69 10.58,3.61 9.79,3.05C9,2.5 8.12,2.5 8.12,2.5C5.38,2.5 3.16,4.72 3.16,7.45V11.05C3.16,12.54 4.38,13.8 5.91,13.8H8.83C10.11,13.8 10.94,12.11 9.58,11.33C9.58,11.3 9.58,10.58 9.58,10.58M20.84,10.58C21.12,11.13 20.73,11.8 20.11,11.8H17.19C16.77,11.8 16.43,11.46 16.43,11.05V8.95C16.43,7.05 18,5.5 19.89,5.5C21,5.5 21.27,4.69 21.27,4.69C21.27,4.69 21.85,3.61 21.06,3.05C20.27,2.5 19.39,2.5 19.39,2.5C16.66,2.5 14.43,4.72 14.43,7.45V11.05C14.43,12.54 15.66,13.8 17.19,13.8H20.11C21.39,13.8 22.22,12.11 20.84,11.33C20.84,11.3 20.84,10.58 20.84,10.58" />
                          </svg>
                        </div>
                        
                          <p className="text-white text-xl md:text-2xl font-light italic leading-relaxed mb-8">
                          {testimonial.quote}
                        </p>
                          
                          {/* Decorative line removed from here since it's now under the stars */}
                        </div>
                      </div>
                    </div>
                    
                    {/* Decorative elements */}
                    <div className="absolute top-0 end-0 w-32 h-32 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 blur-2xl rounded-full"></div>
                    <div className="absolute bottom-0 start-0 w-40 h-40 bg-gradient-to-tr from-indigo-500/10 to-purple-500/10 blur-2xl rounded-full"></div>
                    
                    {/* Accent glow bars */}
                    <div className="absolute top-0 end-10 w-1 h-12 bg-gradient-to-b from-indigo-500/60 to-transparent"></div>
                    <div className="absolute bottom-0 start-1/3 w-1 h-16 bg-gradient-to-b from-transparent to-purple-500/60"></div>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Navigation Arrows - Enhanced */}
            <button
              onClick={goToPrevSlide}
              className="absolute start-2 md:start-6 top-1/2 transform -translate-y-1/2 w-12 h-12 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center text-white hover:bg-gradient-to-r hover:from-indigo-600 hover:to-indigo-700 transition-all duration-300 border border-white/10 shadow-lg"
              aria-label="Previous testimonial"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={goToNextSlide}
              className="absolute end-2 md:end-6 top-1/2 transform -translate-y-1/2 w-12 h-12 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center text-white hover:bg-gradient-to-r hover:from-indigo-600 hover:to-indigo-700 transition-all duration-300 border border-white/10 shadow-lg"
              aria-label="Next testimonial"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Navigation Dots - Enhanced */}
          <div className="flex justify-center mt-10 space-x-3">
            {testimonials.length <= 5 ? (
              // Show all dots if 5 or fewer testimonials
              testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    activeIndex === index 
                      ? "bg-gradient-to-r from-indigo-500 to-purple-500 scale-125" 
                      : "bg-white/30 hover:bg-white/50"
                  }`}
                aria-label={`Go to testimonial ${index + 1}`}
                />
              ))
            ) : (
              // Smart pagination for many testimonials
              <>
                {/* First dot always visible */}
                <button
                  onClick={() => goToSlide(0)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    activeIndex === 0 
                      ? "bg-gradient-to-r from-indigo-500 to-purple-500 scale-125" 
                      : "bg-white/30 hover:bg-white/50"
                  }`}
                  aria-label="Go to first testimonial"
                />
                
                {/* Show ellipsis if active slide is not near the beginning */}
                {activeIndex > 2 && (
                  <span className="text-white/50 text-xs self-center">...</span>
                )}
                
                {/* Show dots around current slide */}
                {activeIndex > 1 && activeIndex < testimonials.length - 2 && (
                  <>
                    {[activeIndex - 1, activeIndex, activeIndex + 1].map(idx => (
                      <button
                        key={idx}
                        onClick={() => goToSlide(idx)}
                        className={`w-3 h-3 rounded-full transition-all duration-300 ${
                          activeIndex === idx 
                            ? "bg-gradient-to-r from-indigo-500 to-purple-500 scale-125" 
                            : "bg-white/30 hover:bg-white/50"
                        }`}
                        aria-label={`Go to testimonial ${idx + 1}`}
                      />
                    ))}
                  </>
                )}
                
                {/* For first few slides, show first 3 dots */}
                {activeIndex <= 1 && (
                  <>
                    {[1, 2].map(idx => (
                      <button
                        key={idx}
                        onClick={() => goToSlide(idx)}
                        className={`w-3 h-3 rounded-full transition-all duration-300 ${
                          activeIndex === idx 
                            ? "bg-gradient-to-r from-indigo-500 to-purple-500 scale-125" 
                            : "bg-white/30 hover:bg-white/50"
                        }`}
                        aria-label={`Go to testimonial ${idx + 1}`}
                      />
                    ))}
                  </>
                )}
                
                {/* For last few slides, show last 3 dots */}
                {activeIndex >= testimonials.length - 2 && activeIndex > 1 && (
                  <>
                    {[testimonials.length - 3, testimonials.length - 2].map(idx => (
                      <button
                        key={idx}
                        onClick={() => goToSlide(idx)}
                        className={`w-3 h-3 rounded-full transition-all duration-300 ${
                          activeIndex === idx 
                            ? "bg-gradient-to-r from-indigo-500 to-purple-500 scale-125" 
                            : "bg-white/30 hover:bg-white/50"
                        }`}
                        aria-label={`Go to testimonial ${idx + 1}`}
                      />
                    ))}
                  </>
                )}
                
                {/* Show ellipsis if active slide is not near the end */}
                {activeIndex < testimonials.length - 3 && (
                  <span className="text-white/50 text-xs self-center">...</span>
                )}
                
                {/* Last dot always visible */}
                <button
                  onClick={() => goToSlide(testimonials.length - 1)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    activeIndex === testimonials.length - 1 
                      ? "bg-gradient-to-r from-indigo-500 to-purple-500 scale-125" 
                      : "bg-white/30 hover:bg-white/50"
                  }`}
                  aria-label="Go to last testimonial"
                />
              </>
            )}
          </div>
        </div>
      </motion.div>
    </section>
  );
};

export default Testimonials; 